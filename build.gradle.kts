plugins {
    id("fabric-loom") version "1.10-SNAPSHOT"
    id("org.jetbrains.kotlin.jvm") version "2.1.21"
}

version = "1.0.0"
group = "oryx"

repositories {
    mavenCentral()
}

dependencies {
    minecraft(group = "com.mojang", name = "minecraft", version = "1.21.5")
    mappings(group = "net.fabricmc", name = "yarn", version = "1.21.5+build.1")
    modImplementation(group = "net.fabricmc", name = "fabric-loader", version = "0.16.14")
    modImplementation(group = "net.fabricmc.fabric-api", name = "fabric-api", version = "0.125.3+1.21.5")
    modImplementation(group = "net.fabricmc", name = "fabric-language-kotlin", version = "1.13.3+kotlin.2.1.21")
    implementation(group = "io.github.spair", name = "imgui-java-app", version = "1.89.0")
}

tasks.processResources {
    inputs.property("version", project.version)
    filesMatching("fabric.mod.json") { expand("version" to project.version) }
}

java {
    withSourcesJar()
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

tasks.jar {
    inputs.property("archivesName", project.base.archivesName.get())
    from("LICENSE") { rename { "${it}_${project.base.archivesName.get()}" } }
}
