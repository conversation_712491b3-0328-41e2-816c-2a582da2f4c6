# New Object-Oriented Command System

The Oryx command system now supports a new object-oriented approach that makes creating commands even more intuitive and organized. You can now define commands as objects that extend the base `Command` class.

## Features

- **Object-Oriented Design**: Commands are now objects that extend a base `Command` class
- **Clean DSL Syntax**: The same powerful DSL is available within the `create()` method
- **Type Safety**: Full compile-time type checking
- **Auto-completion**: Automatic tab completion for all commands and arguments
- **Backward Compatibility**: The old functional DSL approach still works
- **Easy Organization**: Commands can be grouped in files and packages

## Basic Syntax

```kotlin
object YourCommand : Command("commandname", "alias1", "alias2") {
    override fun create() = {
        description = "Your command description"
        category = CommandCategory.UTILITY
        
        // Define arguments, sub-commands, and execution logic here
        execute {
            ChatUtil.send("Command executed!")
            CommandResult.success()
        }
    }
}
```

## Examples

### 1. Simple Command

```kotlin
object PingCommand : Command("ping") {
    override fun create() = {
        description = "Test command that responds with pong"
        category = CommandCategory.UTILITY
        
        execute {
            ChatUtil.send("Pong!")
            CommandResult.success()
        }
    }
}
```

### 2. Command with Arguments and Suggestions

```kotlin
object ToggleCommand : Command("toggle", "t") {
    override fun create() = {
        description = "Toggle modules on/off"
        category = CommandCategory.MODULE
        
        string("module") {
            suggest { ModuleManager.map { it.name } }
        }
        
        execute {
            val moduleName = string("module")
            val module = ModuleManager.firstOrNull { it.name.equals(moduleName, ignoreCase = true) }
            
            if (module != null) {
                module.toggle()
                ChatUtil.send("[${module.name}] toggled ${if (module.enabled) "enabled" else "disabled"}")
                CommandResult.success()
            } else {
                ChatUtil.send("Module '$moduleName' does not exist")
                CommandResult.error()
            }
        }
    }
}
```

### 3. Command with Sub-commands

```kotlin
object ModuleCommand : Command("module", "mod", "m") {
    override fun create() = {
        description = "Module management commands"
        category = CommandCategory.MODULE
        
        subCommand("list", "ls") {
            description = "List all modules"
            
            optionalString("category") {
                suggest("Combat", "Movement", "Render", "Player", "World")
            }
            
            execute {
                val categoryFilter = stringOrNull("category")
                // Implementation here...
                CommandResult.success()
            }
        }
        
        subCommand("info", "i") {
            description = "Get information about a module"
            
            string("module") {
                suggest { ModuleManager.map { it.name } }
            }
            
            execute {
                val moduleName = string("module")
                // Implementation here...
                CommandResult.success()
            }
        }
    }
}
```

### 4. Complex Command with Nested Sub-commands

```kotlin
object ConfigCommand : Command("config", "cfg") {
    override fun create() = {
        description = "Configuration management"
        category = CommandCategory.GENERAL
        
        subCommand("save") {
            description = "Save current configuration"
            
            optionalString("name") {
                suggest("default", "pvp", "legit", "ghost")
            }
            
            execute {
                val name = stringOrNull("name") ?: "default"
                ChatUtil.send("Configuration saved as '$name'")
                CommandResult.success()
            }
        }
        
        subCommand("load") {
            description = "Load a configuration"
            
            string("name") {
                suggest("default", "pvp", "legit", "ghost")
            }
            
            execute {
                val name = string("name")
                ChatUtil.send("Configuration '$name' loaded")
                CommandResult.success()
            }
        }
        
        subCommand("delete", "del") {
            description = "Delete a configuration"
            
            string("name") {
                suggest("pvp", "legit", "ghost") // Don't suggest "default"
            }
            
            execute {
                val name = string("name")
                if (name == "default") {
                    ChatUtil.send("Cannot delete the default configuration")
                    CommandResult.error()
                } else {
                    ChatUtil.send("Configuration '$name' deleted")
                    CommandResult.success()
                }
            }
        }
    }
}
```

## DSL Reference

### Command Definition

```kotlin
object YourCommand : Command("name", "alias1", "alias2") {
    override fun create() = {
        description = "Command description"
        category = CommandCategory.GENERAL // GENERAL, MODULE, UTILITY, DEBUG, ADMIN
        permission = "optional.permission"
        
        // Your command structure here
    }
}
```

### Arguments

```kotlin
// Required string argument
string("argname") {
    description = "Argument description"
    suggest("option1", "option2", "option3")
    // or
    suggest { dynamicListFunction() }
}

// Optional string argument
optionalString("argname") {
    suggest("default", "option1", "option2")
}
```

### Sub-commands

```kotlin
subCommand("subname", "alias1", "alias2") {
    description = "Sub-command description"
    
    // Arguments for this sub-command
    string("arg") {
        suggest("value1", "value2")
    }
    
    // Execution logic
    execute {
        val arg = string("arg")
        ChatUtil.send("Sub-command executed with: $arg")
        CommandResult.success()
    }
}
```

### Execution

```kotlin
execute {
    // Access arguments
    val requiredArg = string("argname")
    val optionalArg = stringOrNull("optionalarg")
    
    // Your logic here
    ChatUtil.send("Command executed!")
    
    // Return result
    CommandResult.success() // or CommandResult.error()
}

// Simplified execution (auto-handles exceptions)
execute {
    ChatUtil.send("Simple execution!")
    // Automatically returns success, catches exceptions
}
```

### Suggestions

```kotlin
string("module") {
    // Static suggestions
    suggest("option1", "option2", "option3")
    
    // Dynamic suggestions
    suggest { ModuleManager.map { it.name } }
    
    // Filtered suggestions
    suggest { 
        ModuleManager.filter { it.enabled }.map { it.name }
    }
}
```

## Registration

### Automatic Registration

Commands are automatically registered when you add them to the `registerBuiltInCommands()` function:

```kotlin
fun CommandManager.registerBuiltInCommands() {
    // Register new object-oriented commands
    register(
        PingCommand,
        ToggleCommand,
        ModuleCommand,
        ConfigCommand,
        UtilityCommand,
        DebugCommand
    )
}
```

### Manual Registration

You can also register commands manually:

```kotlin
// Register a single command
CommandManager.register(YourCommand)

// Register multiple commands
CommandManager.register(Command1, Command2, Command3)
```

## Organization

### File Structure

Organize your commands in packages:

```
src/main/kotlin/oryx/command/
├── Command.kt                    // Base command class
├── CommandManager.kt             // Command manager
├── BuiltInCommands.kt           // Registration
└── commands/                     // Command implementations
    ├── ExampleCommands.kt       // Example commands
    ├── ModuleCommands.kt        // Module-related commands
    ├── UtilityCommands.kt       // Utility commands
    └── AdminCommands.kt         // Admin commands
```

### Grouping Commands

You can group related commands in the same file:

```kotlin
// ModuleCommands.kt
object ToggleCommand : Command("toggle", "t") { /* ... */ }
object BindCommand : Command("bind", "b") { /* ... */ }
object ModuleListCommand : Command("modules", "mods") { /* ... */ }

// UtilityCommands.kt
object PingCommand : Command("ping") { /* ... */ }
object EchoCommand : Command("echo") { /* ... */ }
object TimeCommand : Command("time") { /* ... */ }
```

## Advanced Features

### Inheritance

You can create base command classes for common functionality:

```kotlin
abstract class ModuleCommand(name: String, vararg aliases: String) : Command(name, *aliases) {
    protected fun findModule(name: String) = ModuleManager.firstOrNull { 
        it.name.equals(name, ignoreCase = true) 
    }
    
    protected fun moduleNotFound(name: String) {
        ChatUtil.send("Module '$name' not found!")
    }
}

object ToggleCommand : ModuleCommand("toggle", "t") {
    override fun create() = {
        description = "Toggle modules on/off"
        category = CommandCategory.MODULE
        
        string("module") {
            suggest { ModuleManager.map { it.name } }
        }
        
        execute {
            val moduleName = string("module")
            val module = findModule(moduleName)
            
            if (module != null) {
                module.toggle()
                ChatUtil.send("[${module.name}] toggled ${if (module.enabled) "enabled" else "disabled"}")
                CommandResult.success()
            } else {
                moduleNotFound(moduleName)
                CommandResult.error()
            }
        }
    }
}
```

### Dynamic Command Generation

You can create commands dynamically:

```kotlin
class ModuleToggleCommand(private val module: Module) : Command("toggle${module.name.lowercase()}") {
    override fun create() = {
        description = "Toggle ${module.name} module"
        category = CommandCategory.MODULE
        
        execute {
            module.toggle()
            ChatUtil.send("[${module.name}] toggled ${if (module.enabled) "enabled" else "disabled"}")
            CommandResult.success()
        }
    }
}

// Register dynamic commands
ModuleManager.forEach { module ->
    CommandManager.register(ModuleToggleCommand(module))
}
```

## Migration from Legacy System

### Before (Legacy DSL)

```kotlin
register(command("mycommand") {
    description = "My command"
    category = CommandCategory.UTILITY
    
    stringArg("arg") {
        suggestions = listOf("option1", "option2")
    }
    
    execute {
        val arg = string("arg")
        ChatUtil.send("Argument: $arg")
        CommandResult.success()
    }
})
```

### After (New Object-Oriented)

```kotlin
object MyCommand : Command("mycommand") {
    override fun create() = {
        description = "My command"
        category = CommandCategory.UTILITY
        
        string("arg") {
            suggest("option1", "option2")
        }
        
        execute {
            val arg = string("arg")
            ChatUtil.send("Argument: $arg")
            CommandResult.success()
        }
    }
}
```

## Benefits

### 1. Better Organization
- Commands are objects that can be easily organized in packages
- Related commands can be grouped together
- Easy to find and modify specific commands

### 2. Inheritance Support
- Create base command classes for common functionality
- Share code between related commands
- Implement command patterns and templates

### 3. IDE Support
- Better auto-completion and navigation
- Easier refactoring and renaming
- Better error detection and debugging

### 4. Type Safety
- Full compile-time type checking
- No runtime reflection or string-based lookups
- Catch errors early in development

### 5. Extensibility
- Easy to extend commands with additional functionality
- Plugin systems can easily add new commands
- Dynamic command generation is straightforward

## Best Practices

1. **Use descriptive object names**: `ToggleCommand` instead of `Cmd1`
2. **Group related commands**: Put module commands in `ModuleCommands.kt`
3. **Use inheritance for common functionality**: Create base classes for shared logic
4. **Provide good descriptions**: Help users understand what commands do
5. **Use appropriate categories**: Makes commands easier to find in help
6. **Add suggestions where useful**: Improves user experience
7. **Handle errors gracefully**: Use try-catch or return error results
8. **Follow consistent naming**: Use PascalCase for object names, kebab-case for command names

## Conclusion

The new object-oriented command system provides:

- **🏗️ Better Structure**: Commands are organized as objects
- **🔄 Inheritance**: Share code between related commands  
- **🎯 Type Safety**: Full compile-time checking
- **📚 Readability**: Clean, self-documenting code
- **🛠️ Extensibility**: Easy to extend and customize
- **🔄 Compatibility**: Works alongside the legacy system

This system makes creating and managing commands in Oryx more intuitive and maintainable while preserving all the power and flexibility of the original DSL approach.