{"schemaVersion": 1, "id": "oryx", "version": "${version}", "name": "Oryx", "description": "Oryx client by KhoiBruh", "authors": ["KhoiBruh"], "contact": {"homepage": "https://fabricmc.net/", "sources": "https://github.com/FabricMC/fabric-example-mod"}, "license": "CC0-1.0", "icon": "assets/oryx/icon.png", "environment": "*", "entrypoints": {"main": [{"value": "oryx.Oryx", "adapter": "kotlin"}]}, "mixins": ["oryx.mixins.json"], "depends": {"fabricloader": ">=0.16.14", "minecraft": "~1.21.5", "java": ">=21", "fabric-api": "*", "fabric-language-kotlin": "*"}, "suggests": {"another-mod": "*"}}