package oryx.module

import oryx.event.Event
import oryx.event.EventManager
import oryx.event.Priority
import oryx.utility.MC
import oryx.value.Value
import oryx.value.impl.BooleanValue
import oryx.value.impl.ListValue
import oryx.value.impl.NumberValue
import kotlin.reflect.KClass

abstract class Module : MC {
    private data class ModuleEvent<E : Event>(
        val kClass: KClass<E>,
        val priority: Priority,
        val unit: (E) -> Unit
    )

    private val moduleEvents = HashSet<ModuleEvent<Event>>()
    val values = mutableListOf<Value<*>>()

    val name: String = javaClass.simpleName
    val category = determineCategory()
    open var key = 0
    var enabled = false

    private fun determineCategory(): Category {
        val packageName = javaClass.`package`.name
        val category = packageName.substringAfterLast(".").uppercase()
        return Category.valueOf(category)
    }

    fun <E : Event> add(kClass: KClass<E>, priority: Priority = Priority.NORMAL, unit: (E) -> Unit) {
        val wrapper: (Event) -> Unit = { event -> unit(event as E) }
        moduleEvents.add(ModuleEvent(kClass as KClass<Event>, priority, wrapper))
    }

    fun boolean(
        name: String, value: Boolean,
        hideIf: ((Boolean) -> Boolean)? = null
    ) = BooleanValue(this, name, value, hideIf)

    fun list(
        name: String, value: String, array: Array<String>,
        hideIf: ((String) -> Boolean)? = null
    ) = ListValue(this, name, value, array, hideIf)

    fun number(
        name: String, value: Number, min: Number, max: Number, step: Number,
        hideIf: ((Number) -> Boolean)? = null
    ) = NumberValue(this, name, value, min, max, step, hideIf)

    protected open fun enable() {}
    protected open fun disable() {}

    fun toggle() {
        enabled = !enabled
        if (enabled) {
            enable()
            moduleEvents.forEach { EventManager.add(it.kClass, it.priority, it.unit) }
        } else {
            moduleEvents.forEach { EventManager.remove(it.kClass, it.unit) }
            disable()
        }
    }
}