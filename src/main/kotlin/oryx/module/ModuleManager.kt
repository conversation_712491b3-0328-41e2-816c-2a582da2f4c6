package oryx.module

import org.lwjgl.glfw.GLFW
import oryx.event.EventManager
import oryx.event.impl.KeyEvent
import oryx.module.client.ClickGUI
import oryx.module.move.Sprint
import oryx.utility.Manager

object ModuleManager : Manager<Module>() {
    init {
        add(
            // move
            Sprint,

            // render
            ClickGUI
        )

        EventManager.add(KeyEvent::class) {
            if (it.action == GLFW.GLFW_PRESS) {
                for (module in this) {
                    if (it.key == module.key) module.toggle()
                }
            }
        }
    }

    operator fun get(name: String) = find { it.name.equals(name, ignoreCase = true) }
}