package oryx.ui.clickgui

import net.minecraft.client.gui.DrawContext
import net.minecraft.client.gui.screen.Screen
import net.minecraft.text.Text
import org.joml.Vector2f
import org.joml.Vector4f
import oryx.Oryx
import oryx.utility.MC
import oryx.utility.extension.rect
import oryx.utility.isMouseIn
import oryx.utility.render.Color

class OryxGUI : Screen(Text.literal(Oryx.NAME)), MC {
    val size = Vector4f(0F, 0F, 400F, 220F)
    private val mouse = Vector2f(0F, 0F)
    private val drag = Vector2f(0F, 0F)
    private var dragging = false

    override fun render(context: DrawContext, mouseX: Int, mouseY: Int, partialTicks: Float) {
        mouse.x = mouseX.toFloat()
        mouse.y = mouseY.toFloat()

        if (dragging) {
            size.x = mouse.x + drag.x
            size.y = mouse.y + drag.y
        }

        context.rect(size.x, size.y, size.z, size.w, Color(100, 100, 100, 200))
        super.render(context, mouseX, mouseY, partialTicks)
    }

    override fun mouseClicked(mouseX: Double, mouseY: Double, button: Int): Boolean {
        val mouseXf = mouseX.toFloat()
        val mouseYf = mouseY.toFloat()

        if (isMouseIn(mouseYf, mouseYf, size.x, size.y, size.z, size.w)) {
            drag.x = size.x - mouseXf
            drag.y = size.y - mouseYf
            dragging = true
        }

        return super.mouseClicked(mouseX, mouseY, button)
    }

    override fun mouseReleased(mouseX: Double, mouseY: Double, button: Int): Boolean {
        if (button == 0 && dragging) dragging = false
        return super.mouseReleased(mouseX, mouseY, button)
    }

    override fun close() {
        super.close()
        dragging = false
    }
}