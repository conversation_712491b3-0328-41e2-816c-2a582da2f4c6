package oryx.ui.clickgui

import imgui.ImGui
import imgui.flag.ImGuiCol
import imgui.flag.ImGuiConfigFlags
import imgui.flag.ImGuiWindowFlags
import imgui.gl3.ImGuiImplGl3
import imgui.glfw.ImGuiImplGlfw
import net.minecraft.client.gui.DrawContext
import net.minecraft.client.gui.screen.Screen
import net.minecraft.text.Text
import org.joml.Vector2f
import oryx.Oryx
import oryx.utility.MC

class OryxGUI : Screen(Text.literal(Oryx.NAME)), MC {
    val size = Vector2f(400F, 220F)
    private val implGlfw = ImGuiImplGlfw()
    private val implGl3 = ImGuiImplGl3()

    init {
        val window = mc.window.handle
        ImGui.createContext()
        implGlfw.init(window, true)
        implGl3.init()

        ImGui.getIO().addConfigFlags(ImGuiConfigFlags.NavEnableKeyboard);
        ImGui.getIO().configWindowsMoveFromTitleBarOnly = true
        ImGui.getStyle().setColor(ImGuiCol.TitleBgActive, 0, 0, 0, 255);
    }

    override fun render(context: DrawContext, mouseX: Int, mouseY: Int, partialTicks: Float) {
        implGl3.newFrame()
        implGlfw.newFrame()
        ImGui.newFrame()

        if (ImGui.begin(Oryx.NAME, ImGuiWindowFlags.NoResize)) {
            ImGui.setWindowSize(size.x, size.y)
            ImGui.text("Hello World!")
        }

        ImGui.end()

        ImGui.render()
        implGl3.renderDrawData(ImGui.getDrawData())
        super.render(context, mouseX, mouseY, partialTicks)
    }

    override fun close() {
        mc.setScreen(null)
        implGl3.shutdown()
        implGlfw.shutdown()
        super.close()
    }
}