package oryx.utility

import net.minecraft.entity.Entity
import net.minecraft.util.math.MathHelper
import kotlin.math.sqrt

class Rotation {
    val yaw: Float
    val pitch: Float

    constructor(yaw: Float, pitch: Float) {
        this.yaw = yaw
        this.pitch = pitch
    }

    constructor(entity: Entity) : this(entity.yaw, entity.pitch)

    fun fov(rotation: Rotation) = distance(closestDelta(rotation))
    fun closestDelta(rotation: Rotation) = Rotation(MathHelper.wrapDegrees(rotation.yaw - yaw), rotation.pitch - pitch)
    private fun distance(rotation: Rotation) = sqrt(rotation.yaw * rotation.yaw + rotation.pitch * rotation.pitch)

    operator fun plus(rotation: Rotation) = Rotation(yaw + rotation.yaw, pitch + rotation.pitch)
    operator fun minus(rotation: Rotation) = Rotation(yaw - rotation.yaw, pitch - rotation.pitch)
    operator fun times(value: Float) = Rotation(yaw * value, pitch * value)
    operator fun div(value: Float) = Rotation(yaw / value, pitch / value)
    override fun toString() = "Rotation(yaw = $yaw, pitch = $pitch)"
}