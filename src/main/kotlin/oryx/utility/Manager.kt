package oryx.utility

import kotlin.reflect.KClass

abstract class Manager<A : Any> : ArrayList<A>(), MC {
    fun add(vararg objects: A) {
        objects.forEach { insert(it, size) }
    }

    open fun insert(obj: A, index: Int) {
        if (!contains(obj)) add(index, obj)
    }

    fun remove(vararg objects: A) = removeAll(objects.toSet())

    open operator fun get(kClass: KClass<A>) = find { it::class == kClass }
}