package oryx.utility.render

class Color {
    var red: Int = 0
    var green: Int = 0
    var blue: Int = 0
    var alpha: Int = 0

    constructor(red: Int, green: Int, blue: Int, alpha: Int) {
        this.red = red
        this.green = green
        this.blue = blue
        this.alpha = alpha
    }

    fun rgbToInt(r: Int, g: Int, b: Int): Int {
        val rs = r.toString()
        val gs = g.toString()
        val bs = b.toString()
        return (rs + gs + bs).toInt()
    }

    fun asInt(): Int {
        val a: Int = ((alpha) shl 24) and -0x1000000
        val r = ((red) shl 16) and 0x00FF0000
        val g = ((green) shl 8) and 0x0000FF00
        val b = (blue) and 0x000000FF
        return a or r or g or b
    }

}