package oryx.utility.extension

import net.minecraft.client.gui.DrawContext
import oryx.utility.render.Color
import oryx.utility.render.RenderLayers

fun DrawContext.rect(x: Float, y: Float, width: Float, height: Float, color: Color) {
    val rgb = color.asInt()

    val matrixStack = matrices
    val matrix4f = matrixStack.peek().positionMatrix

    draw {
        val bufferBuilder = it.getBuffer(RenderLayers.QUAD_GUI)
        bufferBuilder.apply {
            vertex(matrix4f, x, y, 0f).color(rgb)
            vertex(matrix4f, x + width, y, 0f).color(rgb)
            vertex(matrix4f, x + width, y + height, 0f).color(rgb)
            vertex(matrix4f, x, y + height, 0f).color(rgb)
        }
    }
}