package oryx.utility.font

import oryx.utility.LoadUtil
import java.awt.Font
import java.util.concurrent.ConcurrentHashMap

enum class Fonts(private val fontName: String, private val extension: String) {
    SF_PRO_ROUNDED("SF-Pro-Rounded-%s", ".ttf"),
    NUNITO("Nunito-%s", ".ttf");

    private val sizes = ConcurrentHashMap<Float, FontRenderer>()

    operator fun get(size: Float) = sizes.computeIfAbsent(size) {
        val fontPath = String.format(fontName, Style.REGULAR)
        val resourcePath = "font/$fontPath$extension"
        val font: Font = LoadUtil.loadFont(resourcePath, it)
        Font<PERSON><PERSON>er(font)
    }

    operator fun get(size: Float, style: Style) = sizes.computeIfAbsent(size) {
        val fontPath = String.format(fontName, style)
        val resourcePath = "font/$fontPath$extension"
        val font: Font = LoadUtil.loadFont(resourcePath, it)
        Font<PERSON><PERSON><PERSON>(font)
    }
}