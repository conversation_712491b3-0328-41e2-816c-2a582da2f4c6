package oryx.utility.font

import com.mojang.blaze3d.opengl.GlStateManager
import org.lwjgl.opengl.GL11.*

data class CharData(val width: Float, val height: Float, val textureId: Int) {
    fun draw(x: Float, y: Float) {
        GlStateManager._bindTexture(textureId)
        glBegin(GL_QUADS)
        glTexCoord2f(0f, 0f)
        glVertex2d(x.toDouble(), y.toDouble())
        glTexCoord2f(0f, 1f)
        glVertex2d(x.toDouble(), (y + height).toDouble())
        glTexCoord2f(1f, 1f)
        glVertex2d((x + width).toDouble(), (y + height).toDouble())
        glTexCoord2f(1f, 0f)
        glVertex2d((x + width).toDouble(), y.toDouble())
        glEnd()
    }
}