package oryx.utility.font

import com.mojang.blaze3d.opengl.GlStateManager
import org.lwjgl.BufferUtils
import org.lwjgl.opengl.GL11.*
import java.awt.Color
import java.awt.Font
import java.awt.Graphics2D
import java.awt.RenderingHints
import java.awt.image.BufferedImage
import java.util.*
import kotlin.math.ceil
import kotlin.math.max

class FontRenderer(private val font: Font) {
    private val charData = arrayOfNulls<CharData?>(1105)
    private val colorCode = '§'
    private val colorCodes = IntArray(32)
    private val margin = 6
    private val random = Random()

    init {
        generateColors()
        for (i in charData.indices) {
            val c = i.toChar()
            if (validChar(c)) setupChar(c)
        }
    }

    fun text(text: String?, x: Float, y: Float, color: Color) {
        render(text, x, y, color.rgb, false)
    }

    fun centeredText(text: String?, x: Float, y: Float, color: Color) {
        render(text, x - (width(text) / 2), y - (height(text) / 2), color.rgb, false)
    }

    fun centeredText(text: String?, x: Float, y: Float, color: Int) {
        render(text, x - (width(text) / 2), y - (height(text) / 2), color, false)
    }

    fun textWithShadow(text: String?, x: Float, y: Float, color: Int) {
        glTranslated(0.5, 0.5, 0.0)
        render(text, x, y, color, true)
        glTranslated(-0.5, -0.5, 0.0)
        render(text, x, y, color, false)
    }

    fun width(text: String?): Float {
        if (text.isNullOrEmpty()) return 0F

        var width = 0F
        val length = text.length

        for (i in 0..<length) {
            val c = text[i]
            val prevChar = if (i > 0) text[i - 1] else '.'

            if (c == colorCode || prevChar == colorCode || !validChar(c)) continue

            val charData: CharData = charData[c.code] ?: continue
            width += (charData.width - (2 * margin)) / 2
        }

        return width
    }

    fun height(text: String?): Float {
        if (text.isNullOrEmpty()) return 0F

        var height = 0F
        val length = text.length

        for (i in 0..<length) {
            val c = text[i]
            val prevChar = if (i > 0) text[i - 1] else '.'

            if (prevChar == colorCode || c == colorCode || !validChar(c)) continue

            val charData: CharData = charData[c.code] ?: continue
            height = height.coerceAtLeast(charData.height)
        }

        return (height - margin) / 2
    }

    private fun render(text: CharSequence?, x: Float, y: Float, color: Int, shadow: Boolean) {
        if (text.isNullOrEmpty()) return

        var posX = x
        var posY = y
        var textColor = color

        glPushMatrix()

        if ((textColor and -0x4000000) == 0) textColor = textColor or -0x1000000
        if (textColor == 0x20FFFFFF) textColor = -0x505051

        glScaled(0.5, 0.5, 0.0)

        posX -= (margin / 2).toFloat()
        posY -= 2F
        posX *= 2F
        posY *= 2F

        var underlined = false
        var strikethrough = false
        var obfuscated = false

        val alpha = (textColor shr 24 and 255) / 255F
        val red = (textColor shr 16 and 255) / 255F
        val green = (textColor shr 8 and 255) / 255F
        val blue = (textColor and 255) / 255F

        GlStateManager._enableBlend()
        glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)

        glColor4f(
            if (shadow) (12 shr 16 and 255) / 255F else red,
            if (shadow) (12 shr 8 and 255) / 255F else green,
            if (shadow) (14 and 255) / 255F else blue,
            alpha
        )

        for (i in text.indices) {
            var c = text[i]
            val prevChar = if (i > 0) text[i - 1] else '.'

            if (prevChar == colorCode) continue

            if (c == colorCode && i + 1 < text.length) {
                val formatChar = text[i + 1]
                val formatIndex = "0123456789ABCDEFKLMNOR".indexOf(formatChar)

                when {
                    formatIndex < 16 -> {
                        obfuscated = false
                        strikethrough = false
                        underlined = false

                        val index = if (formatIndex < 0) 15 else formatIndex
                        val finalIndex = if (shadow) index + 16 else index
                        val colorCode = colorCodes[finalIndex]

                        glColor4f(
                            (colorCode shr 16) / 255F,
                            (colorCode shr 8 and 255) / 255F,
                            (colorCode and 255) / 255F,
                            alpha
                        )
                    }

                    formatIndex == 16 -> obfuscated = true
                    formatIndex == 18 -> strikethrough = true
                    formatIndex == 19 -> underlined = true
                    else -> {
                        obfuscated = false
                        strikethrough = false
                        underlined = false
                        glColor4f(1.0F, 1.0F, 1.0F, alpha)
                    }
                }
            } else {
                if (!validChar(c)) continue

                if (obfuscated) {
                    val randomOFFset = random.nextInt(max(0.0, (charData.size - c.code).toDouble()).toInt())
                    c = (c.code + randomOFFset).toChar()
                }

                val currentChar: CharData = charData[c.code] ?: continue

                currentChar.draw(posX, posY)

                if (strikethrough) line(currentChar.height / 2, currentChar.width, currentChar.height / 2)
                if (underlined) line(currentChar.height - 15, currentChar.width, currentChar.height - 15)

                posX += currentChar.width - (2 * margin)
            }
        }

        glPopMatrix()
        GlStateManager._bindTexture(0)
    }

    private fun setupChar(character: Char) {
        val tempImage = BufferedImage(1, 1, BufferedImage.TYPE_INT_ARGB)
        val tempGraphics = tempImage.graphics as Graphics2D
        tempGraphics.font = font
        val fontMetrics = tempGraphics.fontMetrics
        val charBounds = fontMetrics.getStringBounds(character.toString(), tempGraphics)

        val charImage = BufferedImage(
            ceil(charBounds.width + (2 * margin)).toInt(),
            ceil(charBounds.height).toInt(),
            BufferedImage.TYPE_INT_ARGB
        )

        val graphics = charImage.graphics as Graphics2D
        graphics.font = font
        graphics.color = Color(255, 255, 255, 0)
        graphics.fillRect(0, 0, charImage.width, charImage.height)
        graphics.color = Color.WHITE

        graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON)
        graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)
        graphics.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY)
        graphics.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON)
        graphics.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE)

        graphics.drawString(character.toString(), margin, fontMetrics.ascent)

        val textureId = glGenTextures()
        createTexture(textureId, charImage)

        charData[character.code] = CharData(charImage.width.toFloat(), charImage.height.toFloat(), textureId)
    }

    private fun createTexture(textureId: Int, image: BufferedImage) {
        val pixels = IntArray(image.width * image.height)
        image.getRGB(0, 0, image.width, image.height, pixels, 0, image.width)

        val buffer = BufferUtils.createByteBuffer(image.width * image.height * 4)

        for (y in 0..<image.height) {
            for (x in 0..<image.width) {
                val pixel = pixels[y * image.width + x]
                buffer.put(((pixel shr 16) and 0xFF).toByte())
                buffer.put(((pixel shr 8) and 0xFF).toByte())
                buffer.put((pixel and 0xFF).toByte())
                buffer.put(((pixel shr 24) and 0xFF).toByte())
            }
        }

        buffer.flip()

        GlStateManager._bindTexture(textureId)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR)
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, image.width, image.height, 0, GL_RGBA, GL_UNSIGNED_BYTE, buffer)
    }

    private fun validChar(c: Char) = c.code > 10 && c.code < charData.size && c.code != 127

    private fun generateColors() {
        for (i in 0..31) {
            val base = (i shr 3 and 1) * 85
            var red = (i shr 2 and 1) * 170 + base
            var green = (i shr 1 and 1) * 170 + base
            var blue = (i and 1) * 170 + base

            if (i == 6) red += 85

            if (i >= 16) {
                red /= 4
                green /= 4
                blue /= 4
            }

            colorCodes[i] = (red and 255) shl 16 or ((green and 255) shl 8) or (blue and 255)
        }
    }

    private fun line(y: Float, width: Float, height: Float) {
        glLineWidth(3F)
        glBegin(GL_LINES)
        glVertex2f(0F, y)
        glVertex2f(width, height)
        glEnd()
    }
}