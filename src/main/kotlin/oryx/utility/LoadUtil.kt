package oryx.utility

import oryx.Oryx
import java.awt.Font
import java.awt.FontFormatException
import java.io.IOException

object LoadUtil {
    fun loadFont(path: String?, size: Float): Font {
        try {
            val classLoader = javaClass.classLoader
            classLoader.getResourceAsStream("assets/${Oryx.NAME}/$path").use {
                val font: Font = Font.createFont(Font.TRUETYPE_FONT, it)
                return font.deriveFont(size)
            }
        } catch (e: FontFormatException) {
            Oryx.logger.error("Failed to load font: $path", e)
            return Font("default", Font.PLAIN, size.toInt())
        } catch (e: IOException) {
            Oryx.logger.error("Failed to load font: $path", e)
            return Font("default", Font.PLAIN, size.toInt())
        }
    }
}