package oryx.command

import com.mojang.brigadier.arguments.StringArgumentType
import com.mojang.brigadier.context.CommandContext
import net.minecraft.command.CommandSource

class CommandExecutionContext(private val context: CommandContext<CommandSource>) {
    fun string(name: String): String = StringArgumentType.getString(context, name)
    fun stringOrNull(name: String) = try {
        StringArgumentType.getString(context, name)
    } catch (e: Exception) {
        null
    }
}