package oryx.command.builder

import com.mojang.brigadier.CommandDispatcher
import com.mojang.brigadier.arguments.StringArgumentType
import com.mojang.brigadier.builder.LiteralArgumentBuilder
import com.mojang.brigadier.builder.RequiredArgumentBuilder
import net.minecraft.command.CommandSource
import oryx.command.CommandExecutionContext
import oryx.command.CommandResult

class CommandBuilder(val name: String, vararg val aliases: String) {
    var description: String = ""
    private val arguments = mutableListOf<ParameterBuilder>()
    private var executor: (CommandExecutionContext.() -> CommandResult)? = null
    private val subCommands = mutableListOf<SubCommandBuilder>()

    fun string(name: String, block: ParameterBuilder.() -> Unit = {}) {
        val arg = ParameterBuilder(name)
        arg.block()
        arguments.add(arg)
    }

    fun optionalString(name: String, block: ParameterBuilder.() -> Unit = {}) {
        val arg = ParameterBuilder(name)
        arg.required = false
        arg.block()
        arguments.add(arg)
    }

    fun subCommand(name: String, vararg aliases: String, block: SubCommandBuilder.() -> Unit) {
        val subCmd = SubCommandBuilder(name, *aliases)
        subCmd.block()
        subCommands.add(subCmd)
    }

    fun execute(handler: CommandExecutionContext.() -> CommandResult) {
        executor = handler
    }

    internal fun register(dispatcher: CommandDispatcher<CommandSource>) {
        val mainBuilder = LiteralArgumentBuilder.literal<CommandSource>(name)

        subCommands.forEach { subCmd ->
            val subBuilder = subCmd.build()
            mainBuilder.then(subBuilder)
        }

        if (arguments.isNotEmpty()) {
            buildArgumentChain(mainBuilder, 0)
        } else if (executor != null) {
            mainBuilder.executes { context ->
                val cmdContext = CommandExecutionContext(context)
                executor!!.invoke(cmdContext).code
            }
        }

        val main = dispatcher.register(mainBuilder)

        aliases.forEach { alias ->
            dispatcher.register(LiteralArgumentBuilder.literal<CommandSource>(alias).redirect(main))
        }
    }

    private fun buildArgumentChain(builder: LiteralArgumentBuilder<CommandSource>, index: Int) {
        if (index >= arguments.size) {
            if (executor != null) {
                builder.executes { context ->
                    val cmdContext = CommandExecutionContext(context)
                    executor!!.invoke(cmdContext).code
                }
            }
            return
        }

        val arg = arguments[index]
        val argBuilder =
            RequiredArgumentBuilder.argument<CommandSource, String>(arg.name, StringArgumentType.greedyString())

        val suggestions = arg.suggestions()
        if (suggestions.isNotEmpty()) {
            argBuilder.suggests { _, suggestionsBuilder ->
                suggestions.forEach { suggestion ->
                    if (suggestion.startsWith(suggestionsBuilder.remaining, ignoreCase = true)) {
                        suggestionsBuilder.suggest(suggestion)
                    }
                }
                suggestionsBuilder.buildFuture()
            }
        }

        if (index == arguments.size - 1) {
            if (executor != null) {
                argBuilder.executes { context ->
                    val cmdContext = CommandExecutionContext(context)
                    executor!!.invoke(cmdContext).code
                }
            }
        } else {
            buildArgumentChain(builder, index + 1)
        }

        builder.then(argBuilder)
    }
}