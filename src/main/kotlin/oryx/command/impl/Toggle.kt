package oryx.command.impl

import oryx.command.Command
import oryx.command.CommandResult
import oryx.command.builder.CommandBuilder
import oryx.module.ModuleManager
import oryx.utility.ChatUtil

object Toggle : Command("toggle") {
    override fun create(): CommandBuilder.() -> Unit = {
        description = "Toggle modules on/off"

        string("module") {
            suggest { ModuleManager.map { it.name } }
        }

        execute {
            val moduleName = string("module")
            val module = ModuleManager[moduleName]

            if (module != null) {
                module.toggle()
                ChatUtil.send("[${module.name}] toggled ${if (module.enabled) "enabled" else "disabled"}")
                CommandResult.success()
            } else {
                ChatUtil.send("Module '$moduleName' does not exist")
                CommandResult.error()
            }
        }
    }
}