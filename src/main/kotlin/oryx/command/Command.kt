package oryx.command

import com.mojang.brigadier.CommandDispatcher
import com.mojang.brigadier.arguments.StringArgumentType
import com.mojang.brigadier.context.CommandContext
import net.minecraft.command.CommandSource
import oryx.command.builder.CommandBuilder

class CommandExecutionContext(private val context: CommandContext<CommandSource>) {
    fun string(name: String): String = StringArgumentType.getString(context, name)
    fun stringOrNull(name: String) = try {
        StringArgumentType.getString(context, name)
    } catch (e: Exception) {
        null
    }
}

abstract class Command(val name: String, vararg val alias: String) {
    private var builderContext: CommandBuilder? = null

    abstract fun create(): CommandBuilder.() -> Unit

    internal fun getBuilderContext(): CommandBuilder {
        if (builderContext == null) {
            builderContext = CommandBuilder(name, *alias)
            val createFunction = create()
            createFunction.invoke(builderContext!!)
        }
        return builderContext!!
    }

    internal fun register(dispatcher: CommandDispatcher<CommandSource>) {
        getBuilderContext().register(dispatcher)
    }
}