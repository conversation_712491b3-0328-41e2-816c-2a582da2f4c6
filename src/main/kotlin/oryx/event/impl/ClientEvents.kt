package oryx.event.impl

import net.minecraft.entity.Entity
import net.minecraft.util.math.Box
import net.minecraft.util.math.Vec3d
import oryx.event.Event
import oryx.module.Module

class PreAimPointEvent(val entity: Entity, var aimPoint: Vec3d, val box: Box) : Event()
class PreRandomAimPointEvent(val entity: Entity, var aimPoint: Vec3d, val box: Box) : Event()
class PostAimPointEvent(val entity: Entity, var aimPoint: Vec3d, val box: Box) : Event()
class ModuleStateEvent(val module: Module, val oldState: Boolean, val newState: Boolean) : Event()
class DoneLoadEvent : Event()
