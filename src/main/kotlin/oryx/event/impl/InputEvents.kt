package oryx.event.impl

import net.minecraft.client.option.KeyBinding
import oryx.event.Cancellable
import oryx.event.Event

class KeyEvent(val key: Int, val action: Int) : Cancellable()
class MouseEvent(val button: Int, val action: Int) : Cancellable()
class KeyBindEvent : Event {
    var dirty = false
    val key: KeyBinding
    var pressed = false
        set(value) {
            field = value
            dirty = true
        }

    constructor(key: KeyBinding, pressed: Boolean) : super() {
        this.key = key
        this.pressed = pressed
        dirty = false
    }
}