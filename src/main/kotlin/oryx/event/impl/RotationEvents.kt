package oryx.event.impl

import net.minecraft.client.input.Input
import oryx.event.Event
import oryx.utility.Rotation

class RotationEvent : Event {
    var dirty = false
        private set

    var rotation = Rotation(0F, 0F)
        set(value) {
            field = value
            dirty = true
        }

    constructor(rotation: Rotation) : super() {
        this.rotation = rotation
        dirty = false
    }
}

class VelocityYawEvent(var yaw: Float) : Event()
class InputEvent(val input: Input) : Event()
class MouseDeltaEvent(var deltaX: Double, var deltaY: Double) : Event()
class RotationSetEvent(val yaw: Float, val pitch: Float) : Event()