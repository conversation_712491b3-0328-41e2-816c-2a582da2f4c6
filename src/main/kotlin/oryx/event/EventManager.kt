package oryx.event

import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.reflect.KClass

object EventManager {
    private data class EventListener<E : Event>(
        val priority: Priority,
        val unit: (E) -> Unit,
        val old: Any
    )

    private val listeners = ConcurrentHashMap<KClass<out Event>, CopyOnWriteArrayList<EventListener<in Event>>>()

    fun post(event: Event) = listeners[event::class]?.forEach { it.unit(event) }

    fun <E : Event> add(kClass: KClass<E>, priority: Priority = Priority.NORMAL, unit: (E) -> Unit) {
        listeners.computeIfAbsent(kClass) { CopyOnWriteArrayList() }.also {
            val wrapper: (Event) -> Unit = { event -> unit(event as E) }
            it.add(EventListener(priority, wrapper, unit))
            it.sortByDescending { listener -> listener.priority }
        }
    }

    fun <E : Event> remove(kClass: KClass<E>, unit: (E) -> Unit) {
        listeners[kClass]?.removeAll { it.old == unit }
    }
}