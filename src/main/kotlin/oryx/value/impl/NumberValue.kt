package oryx.value.impl

import oryx.module.Module
import oryx.value.Value
import kotlin.reflect.KProperty

class NumberValue(
    owner: Module,
    name: String,
    value: Number,
    val min: Number,
    val max: Number,
    val step: Number,
    hideIf: (Number.() -> Boolean)? = null
) : Value<Number>(owner, name, value, hideIf) {

    override fun setValue(thisRef: Any?, property: KProperty<*>, value: Number) {
        this.value = value.toDouble().coerceIn(min.toDouble(), max.toDouble())
        updateVisibility()
    }
}