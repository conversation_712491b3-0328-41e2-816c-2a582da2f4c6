package oryx.value.impl

import oryx.module.Module
import oryx.value.Value
import kotlin.reflect.KProperty

class ListValue(
    owner: Module,
    name: String,
    value: String,
    val array: Array<String>,
    hideIf: (String.() -> Boolean)? = null
) : Value<String>(owner, name, value, hideIf) {

    override fun setValue(thisRef: Any?, property: KProperty<*>, value: String) {
        if (value in array) this.value = value
        updateVisibility()
    }
}