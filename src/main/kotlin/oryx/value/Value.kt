package oryx.value

import oryx.module.Module
import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KProperty

abstract class Value<T>(
    private val owner: Module,
    val name: String,
    private val default: T,
    private val hideIf: (T.() -> Boolean)? = null
) : ReadWriteProperty<Any?, T> {

    var value: T = default
    var hidden: Boolean = false
        private set

    init {
        also { owner.values.add(this) }
        updateVisibility()
    }

    override fun getValue(thisRef: Any?, property: KProperty<*>) = value
    override fun setValue(thisRef: Any?, property: KProperty<*>, value: T) {
        this.value = value
        updateVisibility()
    }

    protected fun updateVisibility() {
        hidden = hideIf?.invoke(value) ?: false
    }

    fun reset() {
        value = default
        updateVisibility()
    }
}