package oryx.mixin;

import net.minecraft.client.option.KeyBinding;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;
import oryx.event.EventManager;
import oryx.event.impl.KeyBindEvent;

@Mixin(KeyBinding.class)
public abstract class KeyBindingMixin {

	@Shadow
	private int timesPressed;

	@Inject(method = "isPressed", at = @At("RETURN"), cancellable = true)
	private void isPressed(CallbackInfoReturnable<Boolean> cir) {
		var event = new KeyBindEvent((KeyBinding) (Object) this, cir.getReturnValue());
		EventManager.INSTANCE.post(event);

		if (event.getDirty() && !cir.getReturnValue() && event.getPressed()) timesPressed++;
		var wasPressed = event.getPressed();
		cir.setReturnValue(wasPressed);
	}
}