package oryx.mixin

import com.llamalad7.mixinextras.sugar.Local
import com.mojang.brigadier.CommandDispatcher
import com.mojang.brigadier.ParseResults
import com.mojang.brigadier.StringReader
import com.mojang.brigadier.suggestion.Suggestions
import net.minecraft.client.gui.screen.ChatInputSuggestor
import net.minecraft.client.gui.screen.ChatInputSuggestor.SuggestionWindow
import net.minecraft.client.gui.widget.TextFieldWidget
import net.minecraft.command.CommandSource
import org.spongepowered.asm.mixin.Final
import org.spongepowered.asm.mixin.Mixin
import org.spongepowered.asm.mixin.Shadow
import org.spongepowered.asm.mixin.injection.At
import org.spongepowered.asm.mixin.injection.Inject
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo
import oryx.event.EventManager.post
import oryx.event.impl.InputSuggestionEvent
import java.util.concurrent.CompletableFuture

@Mixin(ChatInputSuggestor::class)
abstract class ChatInputSuggestorMixin {
    @Shadow
    @Final
    var textField: TextFieldWidget? = null

    @Shadow
    var completingSuggestions: Boolean = false

    @Shadow
    private var parse: ParseResults<CommandSource?>? = null

    @Shadow
    private val window: SuggestionWindow? = null

    @Shadow
    private var pendingSuggestions: CompletableFuture<Suggestions?>? = null

    @Shadow
    protected abstract fun showCommandSuggestions()

    @Inject(
        method = ["refresh"],
        at = [At(value = "INVOKE", target = "Lcom/mojang/brigadier/StringReader;canRead()Z", remap = false)],
        cancellable = true
    )
    fun hookCommandSystem(ci: CallbackInfo, @Local reader: StringReader) {
        val event = InputSuggestionEvent(reader)
        post(event)

        val dispatcher: CommandDispatcher<CommandSource?>? = event.dispatcher
        val commandSource = event.commandSource
        if (dispatcher != null && commandSource != null) {
            if (parse == null) parse = dispatcher.parse(reader, commandSource)

            val cursor = textField!!.getCursor()

            if (cursor >= 1 && (window == null || !completingSuggestions)) {
                pendingSuggestions = dispatcher.getCompletionSuggestions(parse, cursor)
                pendingSuggestions!!.thenRun(Runnable {
                    if (pendingSuggestions!!.isDone()) showCommandSuggestions()
                })
            }
            ci.cancel()
        }
    }
}
