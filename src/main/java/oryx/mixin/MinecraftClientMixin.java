package oryx.mixin;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.RunArgs;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Overwrite;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import oryx.Oryx;

@Mixin(MinecraftClient.class)
public class MinecraftClientMixin {

	@Overwrite
	private String getWindowTitle() {
		return Oryx.NAME;
	}

	@Inject(
			method = "<init>",
			at = @At(
					value = "INVOKE",
					target = "Lnet/minecraft/client/MinecraftClient;setOverlay(Lnet/minecraft/client/gui/screen/Overlay;)V",
					shift = At.Shift.AFTER
			)
	)
	private void init(RunArgs args, CallbackInfo ci) {
		Oryx.INSTANCE.load();
	}
}