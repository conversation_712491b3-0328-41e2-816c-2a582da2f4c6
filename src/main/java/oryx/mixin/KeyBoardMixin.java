package oryx.mixin;

import net.minecraft.client.Keyboard;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import oryx.event.EventManager;
import oryx.event.impl.KeyEvent;

@Mixin(Keyboard.class)
public class KeyBoardMixin {

	@Inject(method = "onKey", at = @At("HEAD"), cancellable = true)
	private void onKey(long window, int key, int scancode, int action, int modifiers, CallbackInfo ci) {
		var keyEvent = new KeyEvent(key, action);
		EventManager.INSTANCE.post(keyEvent);
		if (keyEvent.getCancelled()) ci.cancel();
	}
}