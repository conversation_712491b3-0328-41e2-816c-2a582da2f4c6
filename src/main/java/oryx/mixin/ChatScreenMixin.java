package oryx.mixin;

import net.minecraft.client.gui.screen.ChatScreen;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import oryx.event.EventManager;
import oryx.event.impl.ChatEvent;

@Mixin(ChatScreen.class)
public class ChatScreenMixin {

	@Inject(
			method = "sendMessage",
			at = @At(
					value = "INVOKE",
					target = "Ljava/lang/String;startsWith(Ljava/lang/String;)Z"
			),
			cancellable = true
	)
	private void sendMessage(String chatText, boolean addToHistory, CallbackInfo ci) {
		var event = new ChatEvent(chatText);
		EventManager.INSTANCE.post(event);
		if (event.getCancelled()) ci.cancel();
	}
}