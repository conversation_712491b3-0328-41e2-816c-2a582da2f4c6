package oryx.mixin;

import com.llamalad7.mixinextras.sugar.Local;
import com.mojang.brigadier.ParseResults;
import com.mojang.brigadier.StringReader;
import com.mojang.brigadier.suggestion.Suggestions;
import net.minecraft.client.gui.screen.ChatInputSuggestor;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.command.CommandSource;
import org.jetbrains.annotations.Nullable;
import org.spongepowered.asm.mixin.Final;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import oryx.event.EventManager;
import oryx.event.impl.InputSuggestionEvent;

import java.util.concurrent.CompletableFuture;

@Mixin(ChatInputSuggestor.class)
public abstract class ChatInputSuggestorMixin {

	@Shadow
	@Final
	TextFieldWidget textField;

	@Shadow
	boolean completingSuggestions;

	@Shadow
	private @Nullable ParseResults<CommandSource> parse;

	@Shadow
	@Nullable
	private ChatInputSuggestor.@Nullable SuggestionWindow window;

	@Shadow
	private @Nullable CompletableFuture<Suggestions> pendingSuggestions;

	@Shadow
	protected abstract void showCommandSuggestions();

	@Inject(
			method = "refresh",
			at = @At(
					value = "INVOKE",
					target = "Lcom/mojang/brigadier/StringReader;canRead()Z",
					remap = false
			),
			cancellable = true
	)
	public void refresh(CallbackInfo ci, @Local StringReader reader) {
		var event = new InputSuggestionEvent(reader);
		EventManager.INSTANCE.post(event);

		var dispatcher = event.getDispatcher();
		var commandSource = event.getCommandSource();
		if (dispatcher != null && commandSource != null) {
			if (parse == null) parse = dispatcher.parse(reader, commandSource);

			final var cursor = textField.getCursor();

			if (cursor >= 1 && (window == null || !completingSuggestions)) {
				pendingSuggestions = dispatcher.getCompletionSuggestions(parse, cursor);
				pendingSuggestions.thenRun(() -> {
					if (pendingSuggestions.isDone()) showCommandSuggestions();
				});
			}
			ci.cancel();
		}
	}
}
